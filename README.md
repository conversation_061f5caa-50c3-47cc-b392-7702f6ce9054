This is a command prompt based rewriting of https://github.com/AdrienPoupa/chat.
The aim is to obtain good separation of concern based on application, domain and architecture.
The main logic is mostly preserved.
It is not completed yet.
If user enters two consecutive messages to quickly it says "to quick"
If user does that again it disconnects the user.
----------------------------------------------------------------------------------


IMPORTANT NOT: Please copy users.db and chatrooms.db to the folder where the server runs 

----------------------------------------------------------------------------------

